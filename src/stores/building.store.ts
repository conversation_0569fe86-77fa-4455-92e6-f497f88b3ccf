import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { logger } from '@/infrastructure/logging';
import type { Building } from '@/infrastructure/api/geography/buildings/types';
import { mockBuildings } from '@/infrastructure/api/geography/buildings/mock-buildings';

// ---------------------- types ----------------------
export type BuildingStoreType = BuildingState & BuildingActions;

// ---------------------- state ----------------------
interface BuildingState {
    buildings: Building[];
    isLoading: boolean;
}
// ---------------------- default state ----------------------
const DEFAULT_STATE: BuildingState = {
    buildings: [],
    isLoading: false,
};
// ---------------------- actions ----------------------
interface BuildingActions {
    loadBuildings: () => Promise<void>;
}
// ---------------------- store ----------------------
const _buildingStore = (instanceId: string): StateCreator<BuildingStoreType> => {
    return (set): BuildingStoreType => ({
        ...DEFAULT_STATE,

        loadBuildings: async () => {
            set((state: BuildingState) => ({ ...state, isLoading: true }));
            try {
                // Instead of API call, use mock data
                const res = mockBuildings;
                set((state: BuildingState) => ({
                    ...state,
                    buildings: res,
                    isLoading: false,
                }));
                logger.info(`buildingStore(${instanceId}): loadBuildings: loaded mock data`, res);
            } catch (error) {
                logger.error(`buildingStore(${instanceId}): loadBuildings: error: `, error as Error);
                set((state: BuildingState) => ({ ...state, isLoading: false }));
            }
        },
    });
};
// ---------------------- store instances -------------------
export const useBuildingStore = create<BuildingStoreType>()(
    subscribeWithSelector(
        devtools(_buildingStore('buildingStore'), {
            name: 'buildingStore',
            serialize: { options: { maxDepth: 5 } },
        }),
    ),
);
export const createBuildingStore = (instanceId: string) =>
    create<BuildingStoreType>()(
        subscribeWithSelector(devtools(_buildingStore(instanceId), { name: `building-store-${instanceId}` })),
    );
