import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { logger } from '@/infrastructure/logging';
import type { Floor } from '@/infrastructure/api/geography/floors/types';
import { mockFloors } from '@/infrastructure/api/geography/floors/mock-floors';

// ---------------------- types ----------------------
export type FloorStoreType = FloorState & FloorActions;
// ---------------------- state ----------------------
interface FloorState {
    floors: Floor[];
    isLoading: boolean;
}
// ---------------------- default state ----------------------
const DEFAULT_STATE: FloorState = {
    floors: [],
    isLoading: false,
};
// ---------------------- actions ----------------------
interface FloorActions {
    loadFloors: () => Promise<void>;
}
// ---------------------- store ----------------------
const _floorStore = (instanceId: string): StateCreator<FloorStoreType> => {
    return (set): FloorStoreType => ({
        ...DEFAULT_STATE,
        loadFloors: async () => {
            set((state: FloorState) => ({ ...state, isLoading: true }));
            try {
                // Instead of API call, use mock data
                const res = mockFloors;
                set((state: FloorState) => ({
                    ...state,
                    floors: res,
                    isLoading: false,
                }));
                logger.info(`floorStore(${instanceId}): loadFloors: loaded mock data`, res);
            } catch (error) {
                logger.error(`floorStore(${instanceId}): loadFloors: error: `, error as Error);
                set((state: FloorState) => ({ ...state, isLoading: false }));
            }
        },
    });
};
// ---------------------- store instances -------------------
export const useFloorStore = create<FloorStoreType>()(
    subscribeWithSelector(
        devtools(_floorStore('floorStore'), {
            name: 'floorStore',
            serialize: { options: { maxDepth: 5 } },
        }),
    ),
);
export const createFloorStore = (instanceId: string) =>
    create<FloorStoreType>()(
        subscribeWithSelector(devtools(_floorStore(instanceId), { name: `floor-store-${instanceId}` })),
    );
