import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { Event } from '@/infrastructure/api/events/types';
import { mockEvents } from '@/infrastructure/api/events/mock-events';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type EventsStoreType = EventsState & EventsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface EventsState {
    events: Event[];
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: EventsState = {
    events: [],
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface EventsActions {
    loadAllEvents: () => Promise<void>;
    loadAlertEvents: () => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _eventsStore =
    (instanceId: string): StateCreator<EventsStoreType> =>
    (set): EventsStoreType => ({
        ...DEFAULT_STATE,

        // load all events
        loadAllEvents: async () => {
            set({ isLoading: true });
            try {
                const res = mockEvents;
                set({
                    events: res.events,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAllEvents loaded mock data`, res);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAllEvents error`, error as Error);
                set({ isLoading: false });
            }
        },

        // load only alert events
        loadAlertEvents: async () => {
            set({ isLoading: true });
            try {
                const res = mockEvents;
                const alerts = res.events.filter((e) => e.isAlert);
                set({
                    events: alerts,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAlertEvents loaded only alerts`, alerts);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAlertEvents error`, error as Error);
                set({ isLoading: false });
            }
        },
    });

// ----------------------------------------------------------
// ---------------------- store instances -------------------
// ----------------------------------------------------------
export const useEventsStore = create<EventsStoreType>()(
    subscribeWithSelector(devtools(_eventsStore('global'), { name: 'events-store-global' })),
);

export const createEventsStore = (instanceId: string) =>
    create<EventsStoreType>()(
        subscribeWithSelector(devtools(_eventsStore(instanceId), { name: `events-store-${instanceId}` })),
    );
