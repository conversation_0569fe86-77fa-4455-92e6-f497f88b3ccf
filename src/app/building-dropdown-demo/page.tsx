'use client';

import React, { useState } from 'react';
import { BuildingDropdown } from '@/components/common';
import type { Building } from '@/infrastructure/api/geography/buildings/types';

export default function BuildingDropdownDemo() {
    const [selectedBuilding, setSelectedBuilding] = useState<Building | null>(null);
    const [logs, setLogs] = useState<string[]>([]);

    const handleBuildingSelect = (building: Building) => {
        setSelectedBuilding(building);
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [
            ...prev,
            `[${timestamp}] Selected: Building ${building.shortCode} - ${building.name}`
        ]);
    };

    const clearLogs = () => {
        setLogs([]);
    };

    return (
        <div className="min-h-screen bg-gray-900 p-8">
            <div className="max-w-4xl mx-auto">
                <h1 className="text-3xl font-bold text-white mb-8">
                    Building Dropdown Demo
                </h1>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Demo Section */}
                    <div className="space-y-6">
                        <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                            <h2 className="text-xl font-semibold text-white mb-4">
                                Dropdown Component
                            </h2>
                            
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-2">
                                        Default Dropdown
                                    </label>
                                    <BuildingDropdown 
                                        onSelect={handleBuildingSelect}
                                        placeholder="Choose a building..."
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-2">
                                        With Custom Styling
                                    </label>
                                    <BuildingDropdown 
                                        onSelect={handleBuildingSelect}
                                        placeholder="Select Building"
                                        className="w-full max-w-xs"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-300 mb-2">
                                        Pre-selected Building
                                    </label>
                                    <BuildingDropdown 
                                        onSelect={handleBuildingSelect}
                                        selectedBuilding={selectedBuilding}
                                        placeholder="No building selected"
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Selected Building Info */}
                        {selectedBuilding && (
                            <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                                <h3 className="text-lg font-semibold text-white mb-4">
                                    Selected Building Details
                                </h3>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">ID:</span>
                                        <span className="text-white">{selectedBuilding.id}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Name:</span>
                                        <span className="text-white">{selectedBuilding.name}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Short Code:</span>
                                        <span className="text-white">{selectedBuilding.shortCode}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Address:</span>
                                        <span className="text-white text-right max-w-xs">
                                            {selectedBuilding.address}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Total Floors:</span>
                                        <span className="text-white">{selectedBuilding.totalFloors}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Total Rooms:</span>
                                        <span className="text-white">{selectedBuilding.totalRooms}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-400">Total Doors:</span>
                                        <span className="text-white">{selectedBuilding.totalDoors}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    {/* Logs Section */}
                    <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-white">
                                Selection Logs
                            </h3>
                            <button
                                onClick={clearLogs}
                                className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
                            >
                                Clear
                            </button>
                        </div>
                        
                        <div className="bg-gray-900 p-4 rounded border border-gray-600 h-64 overflow-y-auto">
                            {logs.length === 0 ? (
                                <p className="text-gray-500 text-sm">No selections yet...</p>
                            ) : (
                                <div className="space-y-1">
                                    {logs.map((log, index) => (
                                        <div key={index} className="text-green-400 text-sm font-mono">
                                            {log}
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Usage Instructions */}
                <div className="mt-8 bg-gray-800 p-6 rounded-lg border border-gray-700">
                    <h3 className="text-lg font-semibold text-white mb-4">
                        Usage Instructions
                    </h3>
                    <div className="text-gray-300 text-sm space-y-2">
                        <p>• Click on any dropdown to see the available buildings</p>
                        <p>• Select a building to see its details and log the selection</p>
                        <p>• The dropdown automatically loads building data from the store</p>
                        <p>• Each building item shows an icon and the building short code</p>
                        <p>• The component supports custom styling and placeholder text</p>
                    </div>
                </div>
            </div>
        </div>
    );
}
