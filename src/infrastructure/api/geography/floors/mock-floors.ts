import { Floor } from './types';

export const mockFloors: Floor[] = [
    // Building A - North Wing floors
    {
        id: 1,
        name: 'Ground Floor',
        level: 1,
        zoneId: 1,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-15T09:00:00Z',
        updatedAt: '2024-01-20T15:30:00Z'
    },
    {
        id: 2,
        name: 'Second Floor',
        level: 2,
        zoneId: 1,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-15T09:15:00Z',
        updatedAt: '2024-01-20T15:45:00Z'
    },
    // Building A - South Wing floors
    {
        id: 3,
        name: 'Ground Floor',
        level: 1,
        zoneId: 2,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-20T16:00:00Z'
    },
    {
        id: 4,
        name: 'Second Floor',
        level: 2,
        zoneId: 2,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-15T09:45:00Z',
        updatedAt: '2024-01-20T16:15:00Z'
    },
    // Building B - Storage Area Alpha floors
    {
        id: 5,
        name: 'Ground Floor',
        level: 1,
        zoneId: 3,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-10T10:00:00Z',
        updatedAt: '2024-01-18T12:30:00Z'
    },
    // Building B - Office Wing floors
    {
        id: 6,
        name: 'Second Floor',
        level: 2,
        zoneId: 4,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-10T10:15:00Z',
        updatedAt: '2024-01-18T12:45:00Z'
    },
    // Building C - Laboratory Section floors
    {
        id: 7,
        name: 'Ground Floor',
        level: 1,
        zoneId: 5,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-05T11:30:00Z',
        updatedAt: '2024-01-22T17:15:00Z'
    },
    {
        id: 8,
        name: 'Second Floor',
        level: 2,
        zoneId: 5,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-05T11:45:00Z',
        updatedAt: '2024-01-22T17:30:00Z'
    },
    // Building C - Development Area floors
    {
        id: 9,
        name: 'Third Floor',
        level: 3,
        zoneId: 6,
        floorCode: 'F3',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-05T12:00:00Z',
        updatedAt: '2024-01-22T17:45:00Z'
    },
    // Building D - Control Room floors
    {
        id: 10,
        name: 'Ground Floor',
        level: 1,
        zoneId: 7,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-12T08:30:00Z',
        updatedAt: '2024-01-19T14:00:00Z'
    },
    {
        id: 11,
        name: 'Second Floor',
        level: 2,
        zoneId: 7,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-12T08:45:00Z',
        updatedAt: '2024-01-19T14:15:00Z'
    },
    // Building E - Reception Area floors
    {
        id: 12,
        name: 'Ground Floor',
        level: 1,
        zoneId: 8,
        floorCode: 'F1',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-08T13:00:00Z',
        updatedAt: '2024-01-21T10:30:00Z'
    },
    // Building E - Guest Quarters floors
    {
        id: 13,
        name: 'Second Floor',
        level: 2,
        zoneId: 9,
        floorCode: 'F2',
        floorPlanUrl: '/plans/floorPlan-1.png',
        isActive: true,
        createdAt: '2024-01-08T13:15:00Z',
        updatedAt: '2024-01-21T10:45:00Z'
    }
];

// Helper functions
export const getFloorsByZoneId = (zoneId: number): Floor[] => {
    return mockFloors.filter(floor => floor.zoneId === zoneId && floor.isActive);
};

export const getFloorByCode = (floorCode: string, zoneId?: number): Floor | undefined => {
    return mockFloors.find(floor => 
        floor.floorCode === floorCode && 
        (zoneId ? floor.zoneId === zoneId : true)
    );
};

export const getFloorById = (id: number): Floor | undefined => {
    return mockFloors.find(floor => floor.id === id);
};

export const getFloorsByLevel = (level: number): Floor[] => {
    return mockFloors.filter(floor => floor.level === level && floor.isActive);
};