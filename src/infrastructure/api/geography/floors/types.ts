export interface Floor {
    id: number;
    name: string;
    level: number;
    zoneId: number;
    floorCode: string; // e.g., 'F1', 'F2', 'B1'
    floorPlanUrl: string; // Default: '/plans/floorPlan-1.png'
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface FloorReference {
    id: number;
    name: string;
    level: number;
    floorCode: string;
    zoneId: number;
}

export interface FloorStats {
    floorId: number;
    floorName: string;
    floorCode: string;
    level: number;
    totalRooms: number;
    totalDoors: number;
}