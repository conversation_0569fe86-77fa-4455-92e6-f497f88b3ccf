import { Markers } from './types';
import { mockMarkers } from './mock-markers';

/**
 * Marker service for loading marker data from JSON files or fallback to mock data
 */
export class MarkerService {
    private static instance: MarkerService;
    private cache: Map<string, Markers> = new Map();

    private constructor() {}

    public static getInstance(): MarkerService {
        if (!MarkerService.instance) {
            MarkerService.instance = new MarkerService();
        }
        return MarkerService.instance;
    }

    /**
     * Get markers for a specific building and floor
     * @param buildingId - The building ID
     * @param floorId - The floor ID
     * @returns Promise<Markers> - The markers data
     */
    public async getMarkersByBuildingAndFloor(buildingId: number, floorId: number): Promise<Markers> {
        const cacheKey = `b${buildingId}-f${floorId}`;

        // Check cache first
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey)!;
        }

        try {
            // Try to load from JSON file
            const jsonData = await this.loadMarkersFromJson(buildingId, floorId);
            this.cache.set(cacheKey, jsonData);
            return jsonData;
        } catch (error) {
            console.warn(`Failed to load markers from JSON for ${cacheKey}, falling back to mock data:`, error);

            // Fallback to filtered mock data
            const filteredMarkers = this.filterMockMarkers(buildingId, floorId);
            this.cache.set(cacheKey, filteredMarkers);
            return filteredMarkers;
        }
    }

    /**
     * Get all markers (fallback to mock data)
     * @returns Markers - All markers data
     */
    public getAllMarkers(): Markers {
        return mockMarkers;
    }

    /**
     * Load markers from JSON file
     * @param buildingId - The building ID
     * @param floorId - The floor ID
     * @returns Promise<Markers> - The markers data from JSON
     */
    private async loadMarkersFromJson(buildingId: number, floorId: number): Promise<Markers> {
        const fileName = `b${buildingId}-f${floorId}.json`;
        const filePath = `/src/infrastructure/api/markers/data/${fileName}`;

        try {
            // In a real application, you would use fetch or import
            // For now, we'll simulate the JSON loading
            const response = await fetch(filePath);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            return data as Markers;
        } catch {
            // If fetch fails, try dynamic import (for static files)
            try {
                const jsonModule = await import(`./data/${fileName}`);
                return jsonModule.default as Markers;
            } catch (importError) {
                throw new Error(`Failed to load markers from ${fileName}: ${importError}`);
            }
        }
    }

    /**
     * Filter mock markers by building and floor ID
     * @param buildingId - The building ID
     * @param floorId - The floor ID
     * @returns Markers - Filtered markers data
     */
    private filterMockMarkers(buildingId: number, floorId: number): Markers {
        const filteredMarkers = mockMarkers.markers.filter(
            (marker) => marker.buildingId === buildingId && marker.floorId === floorId,
        );

        return {
            markers: filteredMarkers,
        };
    }

    /**
     * Clear cache for a specific building and floor
     * @param buildingId - The building ID
     * @param floorId - The floor ID
     */
    public clearCache(buildingId?: number, floorId?: number): void {
        if (buildingId !== undefined && floorId !== undefined) {
            const cacheKey = `b${buildingId}-f${floorId}`;
            this.cache.delete(cacheKey);
        } else {
            this.cache.clear();
        }
    }

    /**
     * Preload markers for multiple building/floor combinations
     * @param combinations - Array of {buildingId, floorId} objects
     */
    public async preloadMarkers(combinations: Array<{ buildingId: number; floorId: number }>): Promise<void> {
        const promises = combinations.map(({ buildingId, floorId }) =>
            this.getMarkersByBuildingAndFloor(buildingId, floorId),
        );

        await Promise.allSettled(promises);
    }
}

// Export singleton instance
export const markerService = MarkerService.getInstance();

// Export convenience functions
export const getMarkersByBuildingAndFloor = (buildingId: number, floorId: number) =>
    markerService.getMarkersByBuildingAndFloor(buildingId, floorId);

export const getAllMarkers = () => markerService.getAllMarkers();

export const clearMarkerCache = (buildingId?: number, floorId?: number) =>
    markerService.clearCache(buildingId, floorId);
