import { Systems } from './types';

export const mockSystems: Systems = {
    systems: [
        {
            title: 'Fire alarm system',
            iconName: 'fire',
            iconColor: '#E87027',
            metrics: [
                { key: 'Total devices', value: '200', isAlert: false },
                { key: 'Active Alarms', value: '2', isAlert: true },
            ],
        },
        {
            title: 'Access control',
            iconName: 'door',
            iconColor: '#10BCAD',
            metrics: [
                { key: 'Total doors', value: '150', isAlert: false },
                { key: 'Open/Closed', value: '6/144', isAlert: false },
            ],
        },
        {
            title: 'CCTV control',
            iconName: 'camera',
            iconColor: '#877BD7',
            metrics: [
                { key: 'Total cameras', value: '200', isAlert: false },
                { key: 'Active Incidents', value: '2', isAlert: true },
            ],
        },
        {
            title: 'Gate Barriers',
            iconName: 'arm-barrier',
            iconColor: '#5C9DD5',
            metrics: [
                { key: 'Total barriers', value: '200', isAlert: false },
                { key: 'Unauthorized attempts', value: '5', isAlert: true },
            ],
        },
    ],
};
