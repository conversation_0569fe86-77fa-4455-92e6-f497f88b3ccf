'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useBuildingStore } from '@/stores/building.store';
import type { Building } from '@/infrastructure/api/geography/buildings/types';
import { cn } from '@/shared/utils';

interface BuildingDropdownProps {
    onSelect?: (building: Building) => void;
    selectedBuilding?: Building | null;
    placeholder?: string;
    className?: string;
}

const BuildingIcon = () => (
    <svg 
        width="20" 
        height="20" 
        viewBox="0 0 24 24" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className="text-white"
    >
        <path 
            d="M3 21V9L12 2L21 9V21H15V14H9V21H3Z" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
            fill="currentColor"
            fillOpacity="0.8"
        />
    </svg>
);

const ChevronDownIcon = () => (
    <svg 
        width="16" 
        height="16" 
        viewBox="0 0 24 24" 
        fill="none" 
        xmlns="http://www.w3.org/2000/svg"
        className="text-white transition-transform duration-200"
    >
        <path 
            d="M6 9L12 15L18 9" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
        />
    </svg>
);

export const BuildingDropdown: React.FC<BuildingDropdownProps> = ({
    onSelect,
    selectedBuilding,
    placeholder = "Select Building",
    className
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState<Building | null>(selectedBuilding || null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    
    const { buildings, isLoading, loadBuildings } = useBuildingStore();

    useEffect(() => {
        if (buildings.length === 0 && !isLoading) {
            loadBuildings();
        }
    }, [buildings.length, isLoading, loadBuildings]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSelect = (building: Building) => {
        setSelected(building);
        setIsOpen(false);
        onSelect?.(building);
    };

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={cn("relative inline-block", className)} ref={dropdownRef}>
            {/* Main Dropdown Button */}
            <button
                onClick={toggleDropdown}
                className="flex items-center justify-between w-full min-w-[200px] h-[36px] px-2 py-1 
                          bg-[#212633CC] border border-[#1F2937] rounded-[4px] 
                          text-white text-sm font-medium
                          hover:bg-[#212633] transition-colors duration-200
                          focus:outline-none focus:ring-2 focus:ring-[#5C9DD5] focus:ring-opacity-50"
                disabled={isLoading}
            >
                <div className="flex items-center gap-3">
                    <BuildingIcon />
                    <span className="truncate">
                        {isLoading 
                            ? "Loading..." 
                            : selected 
                                ? `Building ${selected.shortCode}` 
                                : placeholder
                        }
                    </span>
                </div>
                <div className={cn(
                    "transition-transform duration-200",
                    isOpen && "rotate-180"
                )}>
                    <ChevronDownIcon />
                </div>
            </button>

            {/* Dropdown Menu */}
            {isOpen && (
                <div className="absolute top-full left-0 right-0 mt-1 z-50
                               bg-[#212633CC] border border-[#1F2937] rounded-[4px]
                               shadow-lg max-h-60 overflow-y-auto">
                    {buildings.length === 0 ? (
                        <div className="px-2 py-4 text-gray-400 text-sm text-center">
                            No buildings available
                        </div>
                    ) : (
                        buildings.map((building) => (
                            <button
                                key={building.id}
                                onClick={() => handleSelect(building)}
                                className="flex items-center gap-3 w-11/12 h-[36px] mb-1 mx-auto px-2 py-4
                                          bg-[#21263380] border border-[#1F2937] rounded-[4px]
                                          text-white text-sm font-medium
                                          hover:border-[#5C9DD5] hover:bg-opacity-20 
                                          transition-colors duration-200
                                          focus:outline-none focus:bg-[#5C9DD5] focus:bg-opacity-20
                                          first:mt-1 last:mb-1 mx-1"
                            >
                                <BuildingIcon />
                                <span className="truncate">Building {building.shortCode}</span>
                            </button>
                        ))
                    )}
                </div>
            )}
        </div>
    );
};

export default BuildingDropdown;
