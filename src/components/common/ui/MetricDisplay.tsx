import React from 'react';
import { cn } from '@/shared/utils/classNameUtils';
import AlertBadge from './AlertBadge';

export interface MetricDisplayProps {
    label: string;
    value: number | string;
    layout?: 'horizontal' | 'vertical';
    highlight?: boolean;
    isAlert?: boolean;
    animate?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
    labelClassName?: string;
    valueClassName?: string;
}

export default function MetricDisplay({
    label,
    value,
    layout = 'horizontal',
    highlight = false,
    isAlert = false,
    animate = true,
    size = 'md',
    className,
    labelClassName,
    valueClassName,
}: MetricDisplayProps) {
    const sizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
    };

    const containerClasses = cn(
        'flex w-full',
        layout === 'horizontal' ? 'justify-between items-center' : 'flex-col items-start gap-1',
        className,
    );

    const labelClasses = cn('text-gray-400 font-normal leading-tight', sizeClasses[size], labelClassName);

    const valueClasses = cn('font-semibold tabular-nums', highlight && !isAlert && 'text-yellow-400', valueClassName);

    return (
        <div className={containerClasses}>
            <span className={labelClasses}>{label}</span>
            <AlertBadge value={value} isAlert={isAlert} animate={animate} size={size} className={valueClasses} />
        </div>
    );
}
