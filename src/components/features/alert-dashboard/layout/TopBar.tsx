'use client';

import React from 'react';
import { TopBarProps } from '../alert-dashboard.types';

/**
 * TopBar Component
 * Header for the alert dashboard
 */
function TopBar({ className }: TopBarProps) {
    return (
        <div
            className={`bg-[#0d131ff2] border-b border-gray-600 flex items-center h-20 ${className || ''}`}
            style={{ gridArea: 'topbar' }}>
            <div className="w-full flex items-center justify-center px-4">
                {/* Center Section - Title */}
                <div className="flex items-center gap-2">
                    <h1 className="text-white text-xl font-semibold">Alert Dashboard</h1>
                </div>
            </div>
        </div>
    );
}

export default TopBar;
