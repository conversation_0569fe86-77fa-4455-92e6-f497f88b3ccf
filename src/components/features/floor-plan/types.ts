export type MarkerType = 'door' | 'camera' | 'fire' | 'gate';

export type MarkerStatus =
    | 'normal'
    | 'open'
    | 'closed'
    | 'locked'
    | 'recording'
    | 'offline'
    | 'motion_detected'
    | 'smoke_detected'
    | 'alarm_triggered'
    | 'maintenance_required';

export type ColorState = 'normal' | 'active' | 'warning' | 'error';

export interface IconConfig {
    type: MarkerType;
    label: string;
    icon: string;
    description: string;
}

export interface ColorStateConfig {
    state: ColorState;
    label: string;
    color: string;
    bgColor: string;
    description: string;
}

export interface PlacedMarker {
    id: string;
    name: string;
    type: MarkerType;
    status: MarkerStatus;
    positionX: number; // percentage x coordinate
    positionY: number; // percentage y coordinate
    exact_x: number; // exact pixel x coordinate
    exact_y: number; // exact pixel y coordinate
    isAlert: boolean;
    floorId: string;
    zoneId: string;
    buildingId: string;
    description: string;
    lastUpdated: string;
    metadata: {
        placedBy: string;
        placedAt: string;
    };
}

export interface MarkerConfig {
    type: MarkerType;
    status: MarkerStatus;
    label: string;
    svgIcon: string; // SVG icon name from public/svg
    description: string;
}

export interface MarkerStatusConfig {
    status: MarkerStatus;
    label: string;
    color: string;
    bgColor: string;
    description: string;
    isAlert: boolean;
}
