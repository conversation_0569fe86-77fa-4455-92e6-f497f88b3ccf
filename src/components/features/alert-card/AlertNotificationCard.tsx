import { useEffect, useMemo, useRef, useState } from 'react';
import { useEventsStore } from '@/stores/alert.store';
import { SvgIcon } from '@/components/common/ui/SvgIcon';
import AlertCard from './AlertCard';

export function AlertNotificationCard() {
    const [open, setOpen] = useState(false);
    const cardRef = useRef<HTMLDivElement>(null);
    const events = useEventsStore((s) => s.events);
    const loadAlertEvents = useEventsStore((s) => s.loadAlertEvents);

    useEffect(() => {
        loadAlertEvents();
    }, [loadAlertEvents]);

    const alertCount = useMemo(() => events.filter((e) => e.isAlert).length, [events]);

    const hasAlerts = alertCount > 0;
    const borderColor = hasAlerts ? '#EC4747' : '#5C9DD5';
    const textColor = hasAlerts ? '#EC4747' : '#5C9DD5';

    return (
        <>
            <div
                ref={cardRef}
                className="flex items-center justify-between rounded-xl px-2 py-1 border relative"
                style={{ borderColor }}>
                <div className="flex items-center gap-2">
                    <span
                        className="flex items-center justify-center rounded-full w-6 h-6 text-white text-xs font-bold"
                        style={{ backgroundColor: borderColor }}>
                        {alertCount}
                    </span>
                    <span className="text-sm font-medium" style={{ color: textColor }}>
                        Alerts
                    </span>
                </div>

                <div className="px-2 cursor-pointer" onClick={() => setOpen(true)}>
                    <SvgIcon name="maximizeScreen" size="default" strokeColor="white" />
                </div>
            </div>

            {open && (
                <div className="fixed inset-0 bg-black/50 z-50" onClick={() => setOpen(false)}>
                    <div
                        className="absolute"
                        style={{
                            top: (cardRef.current?.getBoundingClientRect().bottom ?? 0) + 8,
                            left: cardRef.current?.getBoundingClientRect().left ?? 0,
                        }}>
                        <AlertCard onClose={() => setOpen(false)} />
                    </div>
                </div>
            )}
        </>
    );
}
