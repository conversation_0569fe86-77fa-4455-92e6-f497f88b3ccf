'use client';

import React from 'react';
import { SvgIcon } from '../common/ui/SvgIcon';

// Type definitions
interface AlertCardProps {
    title?: string;
    subheader?: string;
    timestamp?: string;
    zone?: string;
    description?: string;
    publicAddress?: string;
    type?: 'fire' | 'security' | 'emergency';
    onClose?: () => void;
    onAction?: () => void;
}

const AlertCard: React.FC<AlertCardProps> = ({
    title = 'Fire alarm point',
    subheader = 'Some info',
    timestamp = '12/4/2025 - 12:30 pm',
    zone = 'Smoke Detector',
    description = 'Smoke detected near the main conference room.',
    publicAddress = 'Everyone must proceed to Gate 2',
    type = 'fire',
    onClose = () => {},
    onAction = () => {},
}) => {
    const getAlertColor = (alertType: string): string => {
        switch (alertType) {
            case 'fire':
                return 'border-red-500/30 text-red-400';
            case 'security':
                return 'border-orange-500/30 text-orange-400';
            case 'emergency':
                return 'border-yellow-500/30 text-yellow-400';
            default:
                return 'border-red-500/30 text-red-400';
        }
    };

    return (
        <div className={`max-w-md bg-gray-900 text-white rounded-lg shadow-xl border ${getAlertColor(type)}`}>
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-700">
                <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <SvgIcon name="alert" size="default" strokeColor="black" />
                    </div>
                    <div>
                        <h3 className="font-semibold text-sm text-white">{title}</h3>
                        <p className="text-gray-400 text-xs">{subheader}</p>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="px-4 pb-4 space-y-3 mt-3">
                {/* Zone */}
                <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Zone:</span>
                    <span className="text-white">
                        {zone} - {timestamp}
                    </span>
                </div>

                {/* Description */}
                <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Description:</span>
                    <span className="text-white">{description}</span>
                </div>

                {/* Public Address */}
                <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Public address:</span>
                    <span className="text-white">{publicAddress}</span>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-end gap-2 pt-2">
                    <button
                        onClick={onClose}
                        className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Close
                    </button>
                    <button
                        onClick={onAction}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Action text
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AlertCard;
