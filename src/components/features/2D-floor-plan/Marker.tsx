// components/SvgIcon.tsx
'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path } from 'react-konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    onClick?: () => void; // ✅ new prop
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);

    return (
        <Group x={x} y={y} onClick={onClick}>
            {/* Circle always rendered behind */}
            <Circle
                radius={radius}
                fill={circleFill}
                stroke={circleStroke}
                opacity={0.5} // ✅ semi-transparent
                name="device-marker"
                scaleX={0.7} // stretch horizontally → width ≈ 80
                scaleY={0.7}
            />

            {/* Paths rendered on top */}
            {paths.map((p, i) => (
                <Path key={i} data={p.d} fill={pathFill} stroke={pathStroke} offsetX={12} offsetY={12} />
            ))}
        </Group>
    );
}
