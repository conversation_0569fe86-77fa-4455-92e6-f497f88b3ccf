'use client';

import { Stage, Layer, Image as KonvaImage } from 'react-konva';
import useImage from 'use-image';
import { Marker } from './Marker';

interface DeviceMarker {
    id: string;
    type: 'fire-alarm' | 'access-control' | 'cctv' | 'gate-barrier';
    position: { x: number; y: number }; // Percentage-based positioning
    status: 'normal' | 'warning' | 'danger' | 'offline';
    label?: string;
    metadata?: Record<string, unknown>;
}

export interface FloorPlanViewerProps {
    className?: string;
    floorPlanUrl?: string;
    floorPlanAlt?: string;
    selectedFloor?: string;
    selectedBuilding?: string;
    devices?: DeviceMarker[];
    //   alerts?: AlertMarker[];
    onPlanClick?: (coordinates: { x: number; y: number }) => void;
    zoom?: number;
    onZoomChange?: (zoom: number) => void;
    panPosition?: { x: number; y: number };
    onPanChange?: (position: { x: number; y: number }) => void;
    isLoading?: boolean;
    error?: string;
}

export default function FloorPlan({
    className,
    floorPlanUrl = '/2dFloorPlan.png',
    zoom = 1,
    panPosition = { x: 0, y: 0 },
    isLoading,
    error,
}: FloorPlanViewerProps) {
    const [plan] = useImage(floorPlanUrl);
    //   const stageRef = useRef<any>(null);

    //   // Handle clicks on the floor plan (background)
    //   const handleStageClick = (e: any) => {
    //     if (e.target === e.target.getStage()) {
    //       const pointer = stageRef.current?.getPointerPosition();
    //       if (pointer && onPlanClick) {
    //         onPlanClick({ x: pointer.x, y: pointer.y });
    //       }
    //     }
    //   };

    if (isLoading) {
        return <div className="flex items-center justify-center">Loading…</div>;
    }

    if (error) {
        return <div className="text-red-600">Error: {error}</div>;
    }

    return (
        <div className={`w-full h-screen items-start ${className ?? ''}`}>
            {plan && (
                <Stage
                    width={plan.width}
                    height={plan.height}
                    scaleX={zoom}
                    scaleY={zoom}
                    x={panPosition.x}
                    y={panPosition.y}>
                    <Layer>
                        {/* Floor plan image */}
                        <KonvaImage image={plan} width={plan.width} height={plan.height} />

                        <Marker
                            x={200}
                            y={100}
                            fileName="door"
                            radius={30}
                            circleFill="yellow"
                            circleStroke="yellow"
                            pathStroke="white"
                            onClick={() => alert('test events')}
                        />
                        <Marker
                            x={200}
                            y={300}
                            fileName="building"
                            radius={30}
                            circleFill="yellow"
                            circleStroke="yellow"
                            pathStroke="white"
                            onClick={() => alert('test events')}
                        />
                    </Layer>
                </Stage>
            )}
        </div>
    );
}
